"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Volume2,
  VolumeX,
  Headphones,
  Sliders,
  Zap,
  RotateCcw,
  Settings,
  Mic,
  Radio
} from 'lucide-react'

interface DJDeck {
  id: string
  track: any | null
  isPlaying: boolean
  volume: number
  // ... other deck properties
}

interface DJMixer {
  masterVolume: number
  masterGain: GainNode | null
  cueVolume: number
  cueGain: GainNode | null
  channels: {
    [key: string]: {
      volume: number
      highEQ: number
      midEQ: number
      lowEQ: number
      gain: GainNode | null
      eqNodes: {
        high: BiquadFilterNode | null
        mid: BiquadFilterNode | null
        low: BiquadFilterNode | null
      }
    }
  }
}

interface DJMixerProps {
  mixer: DJMixer
  deckA: DJDeck
  deckB: DJDeck
  onMixerUpdate: (updates: Partial<DJMixer>) => void
  className?: string
}

export function DJMixer({
  mixer,
  deckA,
  deckB,
  onMixerUpdate,
  className
}: DJMixerProps) {
  const updateChannelEQ = (channel: string, band: 'high' | 'mid' | 'low', value: number) => {
    onMixerUpdate({
      channels: {
        ...mixer.channels,
        [channel]: {
          ...mixer.channels[channel],
          [`${band}EQ`]: value
        }
      }
    })
  }

  const updateChannelVolume = (channel: string, volume: number) => {
    onMixerUpdate({
      channels: {
        ...mixer.channels,
        [channel]: {
          ...mixer.channels[channel],
          volume
        }
      }
    })
  }

  const renderChannelStrip = (channel: string, deck: DJDeck) => {
    const channelData = mixer.channels[channel]
    
    return (
      <Card key={channel} className="w-32">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-center">Channel {channel}</CardTitle>
          <CardDescription className="text-xs text-center truncate">
            {deck.track?.title || 'No Track'}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Gain */}
          <div className="space-y-2">
            <div className="text-xs text-center text-muted-foreground">GAIN</div>
            <div className="flex justify-center">
              <Slider
                value={[channelData.volume * 100]}
                onValueChange={([value]) => updateChannelVolume(channel, value / 100)}
                max={100}
                step={1}
                orientation="vertical"
                className="h-16"
              />
            </div>
            <div className="text-xs text-center">{Math.round(channelData.volume * 100)}</div>
          </div>

          {/* EQ Section */}
          <div className="space-y-3">
            <div className="text-xs text-center text-muted-foreground">EQ</div>
            
            {/* High EQ */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>HI</span>
                <span>{channelData.highEQ > 0 ? '+' : ''}{channelData.highEQ}</span>
              </div>
              <Slider
                value={[channelData.highEQ]}
                onValueChange={([value]) => updateChannelEQ(channel, 'high', value)}
                min={-20}
                max={20}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Mid EQ */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>MID</span>
                <span>{channelData.midEQ > 0 ? '+' : ''}{channelData.midEQ}</span>
              </div>
              <Slider
                value={[channelData.midEQ]}
                onValueChange={([value]) => updateChannelEQ(channel, 'mid', value)}
                min={-20}
                max={20}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Low EQ */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>LOW</span>
                <span>{channelData.lowEQ > 0 ? '+' : ''}{channelData.lowEQ}</span>
              </div>
              <Slider
                value={[channelData.lowEQ]}
                onValueChange={([value]) => updateChannelEQ(channel, 'low', value)}
                min={-20}
                max={20}
                step={0.1}
                className="w-full"
              />
            </div>
          </div>

          {/* Channel Volume Fader */}
          <div className="space-y-2">
            <div className="text-xs text-center text-muted-foreground">VOLUME</div>
            <div className="flex justify-center">
              <Slider
                value={[channelData.volume * 100]}
                onValueChange={([value]) => updateChannelVolume(channel, value / 100)}
                max={100}
                step={1}
                orientation="vertical"
                className="h-24"
              />
            </div>
            <div className="text-xs text-center">{Math.round(channelData.volume * 100)}</div>
          </div>

          {/* Channel Controls */}
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full h-8 text-xs"
            >
              <Headphones className="w-3 h-3 mr-1" />
              CUE
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="w-full h-8 text-xs"
              onClick={() => {
                // Reset EQ
                updateChannelEQ(channel, 'high', 0)
                updateChannelEQ(channel, 'mid', 0)
                updateChannelEQ(channel, 'low', 0)
              }}
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              RESET
            </Button>
          </div>

          {/* Level Meter */}
          <div className="space-y-1">
            <div className="text-xs text-center text-muted-foreground">LEVEL</div>
            <div className="flex justify-center">
              <div className="w-2 h-16 bg-muted rounded-full relative overflow-hidden">
                <div 
                  className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500 transition-all"
                  style={{ 
                    height: `${deck.isPlaying ? Math.random() * 80 + 20 : 0}%` 
                  }}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("h-full p-4 space-y-4", className)}>
      {/* Mixer Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold flex items-center gap-2">
          <Sliders className="w-5 h-5" />
          DJ Mixer
        </h3>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            Professional Mode
          </Badge>
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Main Mixer Layout */}
      <div className="flex gap-4 justify-center">
        {/* Channel A */}
        {renderChannelStrip('A', deckA)}

        {/* Center Section - Crossfader & Master */}
        <Card className="w-40">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-center">Master Section</CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Crossfader */}
            <div className="space-y-2">
              <div className="text-xs text-center text-muted-foreground">CROSSFADER</div>
              <Slider
                value={[0]} // This would be connected to crossfader state
                onValueChange={([value]) => {
                  // Update crossfader
                }}
                min={-100}
                max={100}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>A</span>
                <span>B</span>
              </div>
            </div>

            {/* Master Volume */}
            <div className="space-y-2">
              <div className="text-xs text-center text-muted-foreground">MASTER</div>
              <div className="flex justify-center">
                <Slider
                  value={[mixer.masterVolume * 100]}
                  onValueChange={([value]) => onMixerUpdate({ masterVolume: value / 100 })}
                  max={100}
                  step={1}
                  orientation="vertical"
                  className="h-24"
                />
              </div>
              <div className="text-xs text-center">{Math.round(mixer.masterVolume * 100)}</div>
            </div>

            {/* Cue Volume */}
            <div className="space-y-2">
              <div className="text-xs text-center text-muted-foreground">CUE</div>
              <div className="flex justify-center">
                <Slider
                  value={[mixer.cueVolume * 100]}
                  onValueChange={([value]) => onMixerUpdate({ cueVolume: value / 100 })}
                  max={100}
                  step={1}
                  orientation="vertical"
                  className="h-16"
                />
              </div>
              <div className="text-xs text-center">{Math.round(mixer.cueVolume * 100)}</div>
            </div>

            {/* Master Controls */}
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full h-8 text-xs"
              >
                <Volume2 className="w-3 h-3 mr-1" />
                BOOTH
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="w-full h-8 text-xs"
              >
                <Mic className="w-3 h-3 mr-1" />
                MIC
              </Button>
            </div>

            {/* Master Level Meter */}
            <div className="space-y-1">
              <div className="text-xs text-center text-muted-foreground">MASTER LEVEL</div>
              <div className="flex justify-center gap-1">
                <div className="w-2 h-20 bg-muted rounded-full relative overflow-hidden">
                  <div 
                    className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500 transition-all"
                    style={{ 
                      height: `${(deckA.isPlaying || deckB.isPlaying) ? Math.random() * 90 + 10 : 0}%` 
                    }}
                  />
                </div>
                <div className="w-2 h-20 bg-muted rounded-full relative overflow-hidden">
                  <div 
                    className="absolute bottom-0 w-full bg-gradient-to-t from-green-500 via-yellow-500 to-red-500 transition-all"
                    style={{ 
                      height: `${(deckA.isPlaying || deckB.isPlaying) ? Math.random() * 90 + 10 : 0}%` 
                    }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Channel B */}
        {renderChannelStrip('B', deckB)}
      </div>

      {/* Effects Section */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Master Effects
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            {['Filter', 'Delay', 'Reverb', 'Flanger'].map((effect) => (
              <div key={effect} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium">{effect}</span>
                  <Switch />
                </div>
                <Slider
                  value={[0]}
                  onValueChange={() => {}}
                  min={0}
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
