"use client"

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  RotateCw,
  Volume2,
  VolumeX,
  Headphones,
  Disc3,
  Zap,
  Repeat,
  Shuffle,
  SkipBack,
  SkipForward
} from 'lucide-react'

import type { DJDeck, CuePoint, Crossfader, Track } from '../types'

interface DualDeckInterfaceProps {
  deckA: DJDeck
  deckB: DJDeck
  crossfader: Crossfader
  onDeckUpdate: (deckId: string, updates: Partial<DJDeck>) => void
  onCrossfaderUpdate: (updates: Partial<Crossfader>) => void
  onLoadTrack: (deckId: 'A' | 'B', track: Track) => void
  className?: string
}

export function DualDeckInterface({
  deckA,
  deckB,
  crossfader,
  onDeckUpdate,
  onCrossfaderUpdate,
  onLoadTrack,
  className
}: DualDeckInterfaceProps) {
  const [draggedTrack, setDraggedTrack] = useState<any>(null)

  const handlePlay = (deckId: string) => {
    const deck = deckId === 'A' ? deckA : deckB
    onDeckUpdate(deckId, { isPlaying: !deck.isPlaying })
  }

  const handleCue = (deckId: string) => {
    onDeckUpdate(deckId, { currentTime: 0, isPlaying: false })
  }

  const handleSync = (deckId: string) => {
    const sourceDeck = deckId === 'A' ? deckB : deckA
    const targetDeck = deckId === 'A' ? deckA : deckB
    
    if (sourceDeck.track && targetDeck.track) {
      const syncedTempo = sourceDeck.bpm / targetDeck.detectedBpm
      onDeckUpdate(deckId, { 
        tempo: syncedTempo,
        isSync: !targetDeck.isSync 
      })
    }
  }

  const handlePitchBend = (deckId: string, direction: 'up' | 'down') => {
    const deck = deckId === 'A' ? deckA : deckB
    const bendAmount = direction === 'up' ? 0.02 : -0.02
    onDeckUpdate(deckId, { pitch: deck.pitch + bendAmount })
  }

  const renderDeck = (deck: DJDeck, deckId: 'A' | 'B', side: 'left' | 'right') => (
    <div className={cn(
      "flex-1 bg-card border rounded-lg p-4 space-y-4",
      side === 'left' ? 'mr-2' : 'ml-2'
    )}>
      {/* Deck Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Disc3 className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">Deck {deckId}</h3>
          {deck.isSync && (
            <Badge variant="secondary" className="text-xs">SYNC</Badge>
          )}
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{deck.bpm.toFixed(1)} BPM</span>
          <span>{deck.key}</span>
        </div>
      </div>

      {/* Track Info */}
      {deck.track ? (
        <div className="space-y-2">
          <div className="text-sm font-medium truncate">{deck.track.title}</div>
          <div className="text-xs text-muted-foreground truncate">{deck.track.artist}</div>
          
          {/* Waveform Display */}
          <div className="h-16 bg-muted rounded relative overflow-hidden">
            {deck.waveform && (
              <canvas 
                className="w-full h-full"
                // Waveform rendering will be implemented
              />
            )}
            {/* Playhead */}
            <div 
              className="absolute top-0 bottom-0 w-px bg-primary z-10"
              style={{ 
                left: `${(deck.currentTime / deck.duration) * 100}%` 
              }}
            />
            {/* Beat Grid */}
            {deck.beatGrid.map((beat, index) => (
              <div
                key={index}
                className="absolute top-0 bottom-0 w-px bg-white/30"
                style={{ left: `${(beat / deck.duration) * 100}%` }}
              />
            ))}
          </div>

          {/* Progress Bar */}
          <div className="space-y-1">
            <Progress value={(deck.currentTime / deck.duration) * 100} />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{formatTime(deck.currentTime)}</span>
              <span>{formatTime(deck.duration)}</span>
            </div>
          </div>
        </div>
      ) : (
        <div 
          className="h-32 border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center text-muted-foreground"
          onDrop={(e) => {
            e.preventDefault()
            // Handle track drop
            if (draggedTrack) {
              onLoadTrack(deckId, draggedTrack)
              setDraggedTrack(null)
            }
          }}
          onDragOver={(e) => e.preventDefault()}
        >
          <div className="text-center">
            <Disc3 className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Drop track here or browse library</p>
          </div>
        </div>
      )}

      {/* Transport Controls */}
      <div className="flex items-center justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleCue(deckId)}
          className="w-12 h-12"
        >
          <Square className="w-4 h-4" />
        </Button>
        
        <Button
          variant={deck.isPlaying ? "default" : "outline"}
          size="sm"
          onClick={() => handlePlay(deckId)}
          className="w-12 h-12"
        >
          {deck.isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5" />
          )}
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handleSync(deckId)}
          className={cn("w-12 h-12", deck.isSync && "bg-primary text-primary-foreground")}
        >
          <Zap className="w-4 h-4" />
        </Button>
      </div>

      {/* Pitch/Tempo Controls */}
      <div className="space-y-3">
        {/* Pitch Bend */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onMouseDown={() => handlePitchBend(deckId, 'up')}
            className="flex-1"
          >
            <RotateCw className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onMouseDown={() => handlePitchBend(deckId, 'down')}
            className="flex-1"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
        </div>

        {/* Tempo Slider */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Tempo</span>
            <span>{((deck.tempo - 1) * 100).toFixed(1)}%</span>
          </div>
          <Slider
            value={[deck.tempo]}
            onValueChange={([value]) => onDeckUpdate(deckId, { tempo: value })}
            min={0.8}
            max={1.2}
            step={0.001}
            className="w-full"
          />
        </div>

        {/* Volume */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Volume</span>
            <span>{Math.round(deck.volume * 100)}%</span>
          </div>
          <Slider
            value={[deck.volume]}
            onValueChange={([value]) => onDeckUpdate(deckId, { volume: value })}
            min={0}
            max={1}
            step={0.01}
            className="w-full"
          />
        </div>
      </div>

      {/* Hot Cues */}
      <div className="grid grid-cols-4 gap-1">
        {[1, 2, 3, 4].map((cueNum) => {
          const cuePoint = deck.cuePoints.find(c => c.label === cueNum.toString())
          return (
            <Button
              key={cueNum}
              variant={cuePoint ? "default" : "outline"}
              size="sm"
              className="h-8 text-xs"
              onClick={() => {
                if (cuePoint) {
                  onDeckUpdate(deckId, { currentTime: cuePoint.time })
                } else {
                  // Set new cue point
                  const newCue: CuePoint = {
                    id: `cue-${deckId}-${cueNum}`,
                    time: deck.currentTime,
                    label: cueNum.toString(),
                    color: `hsl(${cueNum * 90}, 70%, 50%)`
                  }
                  onDeckUpdate(deckId, {
                    cuePoints: [...deck.cuePoints, newCue]
                  })
                }
              }}
            >
              {cueNum}
            </Button>
          )
        })}
      </div>
    </div>
  )

  return (
    <div className={cn("h-full flex flex-col p-4 space-y-4", className)}>
      {/* Dual Deck Layout */}
      <div className="flex-1 flex">
        {renderDeck(deckA, 'A', 'left')}
        {renderDeck(deckB, 'B', 'right')}
      </div>

      {/* Crossfader Section */}
      <div className="bg-card border rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-medium">Crossfader</h4>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {crossfader.curve.toUpperCase()}
            </Badge>
          </div>
        </div>
        
        <div className="space-y-3">
          {/* Crossfader Slider */}
          <div className="relative">
            <Slider
              value={[crossfader.position]}
              onValueChange={([value]) => onCrossfaderUpdate({ position: value })}
              min={-1}
              max={1}
              step={0.01}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>A</span>
              <span>CENTER</span>
              <span>B</span>
            </div>
          </div>

          {/* Crossfader Curve Selection */}
          <div className="flex gap-1">
            {['linear', 'logarithmic', 'exponential'].map((curve) => (
              <Button
                key={curve}
                variant={crossfader.curve === curve ? "default" : "outline"}
                size="sm"
                onClick={() => onCrossfaderUpdate({ curve: curve as any })}
                className="flex-1 text-xs"
              >
                {curve.charAt(0).toUpperCase()}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
