"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  <PERSON><PERSON>les,
  TrendingUp,
  Heart,
  Play,
  Plus,
  Shuffle,
  Radio,
  Music,
  Disc3,
  Zap,
  RefreshCw
} from 'lucide-react'

interface Track {
  id: string
  title: string
  artist: string
  duration: number
  bpm: number
  key: string
  genre: string
  mood: string
  energy: number
  danceability: number
  artwork?: string
  audioUrl: string
  isLiked: boolean
  playCount: number
  compatibility?: number // DJ compatibility score with current session
}

interface MoodContentBridgeProps {
  currentMood?: string
  onTrackDiscover: (track: Track) => void
  className?: string
}

const MOOD_COLORS = {
  energetic: '#ff6b6b',
  chill: '#4ecdc4',
  dark: '#45b7d1',
  uplifting: '#96ceb4',
  melancholic: '#feca57',
  aggressive: '#ff9ff3',
  peaceful: '#54a0ff',
  mysterious: '#5f27cd'
}

export function MoodContentBridge({
  currentMood = 'energetic',
  onTrackDiscover,
  className
}: MoodContentBridgeProps) {
  const [discoveredTracks, setDiscoveredTracks] = useState<Track[]>([])
  const [trendingTracks, setTrendingTracks] = useState<Track[]>([])
  const [isDiscovering, setIsDiscovering] = useState(false)
  const [discoveryProgress, setDiscoveryProgress] = useState(0)

  // Mock AI-powered track discovery based on current mood and DJ context
  const discoverTracks = async () => {
    setIsDiscovering(true)
    setDiscoveryProgress(0)

    // Simulate AI discovery process
    const interval = setInterval(() => {
      setDiscoveryProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 200)

    // Simulate API call to HVPPY's mood-based discovery engine
    setTimeout(() => {
      const mockDiscoveredTracks: Track[] = [
        {
          id: 'discovered-1',
          title: 'Electric Pulse',
          artist: 'Voltage',
          duration: 210,
          bpm: 132,
          key: 'Em',
          genre: 'Techno',
          mood: currentMood,
          energy: 0.9,
          danceability: 0.85,
          audioUrl: '/audio/discovered1.mp3',
          isLiked: false,
          playCount: 0,
          compatibility: 0.92
        },
        {
          id: 'discovered-2',
          title: 'Neon Dreams',
          artist: 'Cyber DJ',
          duration: 195,
          bpm: 128,
          key: 'Am',
          genre: 'Synthwave',
          mood: currentMood,
          energy: 0.8,
          danceability: 0.9,
          audioUrl: '/audio/discovered2.mp3',
          isLiked: false,
          playCount: 0,
          compatibility: 0.88
        },
        {
          id: 'discovered-3',
          title: 'Bass Revolution',
          artist: 'Low Frequency',
          duration: 240,
          bpm: 140,
          key: 'Dm',
          genre: 'Drum & Bass',
          mood: currentMood,
          energy: 0.95,
          danceability: 0.8,
          audioUrl: '/audio/discovered3.mp3',
          isLiked: false,
          playCount: 0,
          compatibility: 0.85
        }
      ]

      setDiscoveredTracks(mockDiscoveredTracks)
      setIsDiscovering(false)
    }, 2000)
  }

  // Load trending tracks for current mood
  useEffect(() => {
    const mockTrendingTracks: Track[] = [
      {
        id: 'trending-1',
        title: 'Midnight Groove',
        artist: 'Night Rider',
        duration: 220,
        bpm: 124,
        key: 'C',
        genre: 'House',
        mood: currentMood,
        energy: 0.7,
        danceability: 0.9,
        audioUrl: '/audio/trending1.mp3',
        isLiked: true,
        playCount: 2500,
        compatibility: 0.9
      },
      {
        id: 'trending-2',
        title: 'Solar Flare',
        artist: 'Cosmic DJ',
        duration: 180,
        bpm: 130,
        key: 'F#',
        genre: 'Trance',
        mood: currentMood,
        energy: 0.85,
        danceability: 0.95,
        audioUrl: '/audio/trending2.mp3',
        isLiked: false,
        playCount: 1800,
        compatibility: 0.87
      }
    ]

    setTrendingTracks(mockTrendingTracks)
  }, [currentMood])

  const renderTrackCard = (track: Track, type: 'discovered' | 'trending') => (
    <Card 
      key={track.id} 
      className="cursor-pointer hover:shadow-md transition-shadow group"
      onClick={() => onTrackDiscover(track)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-sm truncate">{track.title}</CardTitle>
            <CardDescription className="text-xs truncate">{track.artist}</CardDescription>
          </div>
          <div className="flex items-center gap-1 ml-2">
            {type === 'discovered' && (
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-2 h-2 mr-1" />
                New
              </Badge>
            )}
            {type === 'trending' && (
              <Badge variant="secondary" className="text-xs">
                <TrendingUp className="w-2 h-2 mr-1" />
                Hot
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          {/* Track Stats */}
          <div className="flex items-center gap-2 text-xs">
            <Badge variant="outline" className="px-1">
              {track.bpm} BPM
            </Badge>
            <Badge variant="outline" className="px-1">
              {track.key}
            </Badge>
            <Badge variant="outline" className="px-1">
              {track.genre}
            </Badge>
          </div>

          {/* Compatibility Score */}
          {track.compatibility && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">DJ Compatibility</span>
                <span className="font-medium">{Math.round(track.compatibility * 100)}%</span>
              </div>
              <Progress value={track.compatibility * 100} className="h-1" />
            </div>
          )}

          {/* Energy & Danceability */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-muted-foreground">Energy</span>
              <div className="flex items-center gap-1">
                <div className="flex-1 bg-muted rounded-full h-1">
                  <div 
                    className="h-1 bg-primary rounded-full transition-all"
                    style={{ width: `${track.energy * 100}%` }}
                  />
                </div>
                <span className="text-xs">{Math.round(track.energy * 100)}</span>
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Dance</span>
              <div className="flex items-center gap-1">
                <div className="flex-1 bg-muted rounded-full h-1">
                  <div 
                    className="h-1 bg-primary rounded-full transition-all"
                    style={{ width: `${track.danceability * 100}%` }}
                  />
                </div>
                <span className="text-xs">{Math.round(track.danceability * 100)}</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="outline" size="sm" className="flex-1 h-7 text-xs">
              <Play className="w-3 h-3 mr-1" />
              Preview
            </Button>
            <Button variant="outline" size="sm" className="flex-1 h-7 text-xs">
              <Disc3 className="w-3 h-3 mr-1" />
              Load
            </Button>
            <Button variant="outline" size="sm" className="h-7 px-2">
              <Heart className={cn("w-3 h-3", track.isLiked && "fill-red-500 text-red-500")} />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={cn("h-full bg-card border-l flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-primary" />
            Mood Discovery
          </h3>
          <Badge 
            variant="outline" 
            className="text-xs"
            style={{ 
              backgroundColor: MOOD_COLORS[currentMood as keyof typeof MOOD_COLORS] + '20',
              borderColor: MOOD_COLORS[currentMood as keyof typeof MOOD_COLORS]
            }}
          >
            {currentMood}
          </Badge>
        </div>

        {/* AI Discovery Button */}
        <Button
          onClick={discoverTracks}
          disabled={isDiscovering}
          className="w-full h-8 text-xs"
          variant="outline"
        >
          {isDiscovering ? (
            <>
              <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
              Discovering...
            </>
          ) : (
            <>
              <Zap className="w-3 h-3 mr-2" />
              AI Discover
            </>
          )}
        </Button>

        {/* Discovery Progress */}
        {isDiscovering && (
          <div className="mt-2">
            <Progress value={discoveryProgress} className="h-1" />
            <p className="text-xs text-muted-foreground mt-1">
              Analyzing mood patterns...
            </p>
          </div>
        )}
      </div>

      {/* Content */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {/* Discovered Tracks */}
          {discoveredTracks.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Sparkles className="w-3 h-3" />
                Fresh Discoveries
              </h4>
              <div className="space-y-2">
                {discoveredTracks.map(track => renderTrackCard(track, 'discovered'))}
              </div>
            </div>
          )}

          {/* Trending Tracks */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-3 h-3" />
              Trending in {currentMood}
            </h4>
            <div className="space-y-2">
              {trendingTracks.map(track => renderTrackCard(track, 'trending'))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm" className="h-8 text-xs">
                <Shuffle className="w-3 h-3 mr-1" />
                Random Mix
              </Button>
              <Button variant="outline" size="sm" className="h-8 text-xs">
                <Radio className="w-3 h-3 mr-1" />
                Live Radio
              </Button>
            </div>
          </div>

          {/* Mood Suggestions */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Explore Moods</h4>
            <div className="grid grid-cols-2 gap-1">
              {Object.entries(MOOD_COLORS).slice(0, 6).map(([mood, color]) => (
                <Button
                  key={mood}
                  variant="outline"
                  size="sm"
                  className="h-7 text-xs justify-start"
                  style={{ borderColor: color }}
                  onClick={() => {
                    // Switch to different mood
                  }}
                >
                  <div 
                    className="w-2 h-2 rounded-full mr-2"
                    style={{ backgroundColor: color }}
                  />
                  {mood}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
